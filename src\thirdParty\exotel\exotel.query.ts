import { Injectable } from '@nestjs/common';
import { PgService } from 'src/database/pg/pg.service';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { getMD5Hash } from 'src/utils/crypt';
import { MasterEntity } from 'src/database/pg/entities/master.entity';
import { Includeable } from 'sequelize';
@Injectable()
export class ExotelQuery {
  constructor(private readonly pg: PgService) {}

  async getCompleteUserDetails(userPhone) {
    const hashPhone = getMD5Hash(userPhone);

    const masterInc: Includeable = { model: MasterEntity };
    masterInc.attributes = ['id', 'assignedCSE'];
    const options = {
      attributes: ['id', 'fullName', 'stage', 'masterId', 'lastLoanId'],
      where: { hashPhone },
      include: [masterInc],
    };
    const userData = await this.pg.findOne(registeredUsers, options);
    const loanId = userData?.lastLoanId;
    let loanData: any = {};
    if (loanId) {
      const loanOptions = {
        attributes: ['id', 'loanStatus', 'followerId'],
        where: { id: loanId },
        order: [['id', 'DESC']],
      };
      loanData = await this.pg.findOne(loanTransaction, loanOptions);
    }
    return { userData, loanData };
  }

  async getCompleteUsersDetails(userIds) {
    const masterInc: Includeable = { model: MasterEntity };
    masterInc.attributes = ['assignedCSE'];

    const userOptions = {
      attributes: ['id', 'fullName', 'stage', 'lastLoanId', 'masterId'],
      where: { id: userIds },
      order: [['createdAt', 'DESC']],
      include: [masterInc],
    };
    const userData = await this.pg.findAll(registeredUsers, userOptions);

    const loanIds = userData
      .map((user) => user?.lastLoanId)
      .filter((id) => id != null);
    const loanOptions = {
      attributes: ['id', 'loanStatus', 'followerId'],
      where: { id: loanIds },
      order: [['id', 'DESC']],
    };
    const loanData = await this.pg.findAll(loanTransaction, loanOptions);
    return { userData, loanData };
  }
}
