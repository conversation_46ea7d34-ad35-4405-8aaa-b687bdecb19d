// Imports
import { Injectable } from '@nestjs/common';
import { kUserStageEnums } from 'src/constant/objects';

@Injectable()
export class EnumService {
  getUserStage(stage: number) {
    let user_stage = '';
    switch (stage) {
      case 1:
        user_stage = kUserStageEnums.PHONE_VERIFICATION;
        break;
      case 2:
        user_stage = kUserStageEnums.BASIC_DETAILS;
        break;
      case 3:
        user_stage = kUserStageEnums.SELFIE;
        break;
      case 4:
        user_stage = kUserStageEnums.NOT_ELIGIBLE;
        break;
      case 5:
        user_stage = kUserStageEnums.PIN;
        break;
      case 6:
        user_stage = kUserStageEnums.AADHAAR;
        break;
      case 7:
        user_stage = kUserStageEnums.EMPLOYMENT;
        break;
      case 8:
        user_stage = kUserStageEnums.BANKING;
        break;
      case 9:
        user_stage = kUserStageEnums.RESIDENCE;
        break;
      case 10:
        user_stage = kUserStageEnums.LOAN_ACCEPT;
        break;
      case 11:
        user_stage = kUserStageEnums.CONTACT;
        break;
      case 12:
        user_stage = kUserStageEnums.PAN;
        break;
      case 13:
        user_stage = kUserStageEnums.FINAL_VERIFICATION;
        break;
      case 14:
        user_stage = kUserStageEnums.MANDATE;
        break;
      case 15:
        user_stage = kUserStageEnums.ESIGN;
        break;
      case 16:
        user_stage = kUserStageEnums.DISBURSEMENT;
        break;
      case 17:
        user_stage = kUserStageEnums.REPAYMENT;
        break;
      case 18:
        user_stage = kUserStageEnums.DEFAULTER;
        break;
      case 19:
        user_stage = kUserStageEnums.REAPPLY;
        break;
      case 20:
        user_stage = kUserStageEnums.NO_ROUTE;
        break;
      case 21:
        user_stage = kUserStageEnums.EXPRESS_REAPPLY;
        break;
      case 23:
        user_stage = kUserStageEnums.ON_HOLD;
        break;
      default:
        break;
    }

    return user_stage;
  }
}
