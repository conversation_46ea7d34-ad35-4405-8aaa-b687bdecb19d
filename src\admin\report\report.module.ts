// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportQueryService } from './report.query';
import { PgModule } from 'src/database/pg/pg.module';
import { ReportController } from './report.controller';
import { UtilsModule } from 'src/utils/utils.module';
import { CommonModule } from 'src/common/common.module';
import { ExotelQuery } from 'src/thirdParty/exotel/exotel.query';
import { DataCodesModule } from 'src/neighbours/data-codes/data.codes.module';

@Module({
  controllers: [ReportController],
  imports: [DataCodesModule, PgModule, UtilsModule, CommonModule],
  providers: [ReportQueryService, ReportService, ExotelQuery],
})
export class ReportModule {}
