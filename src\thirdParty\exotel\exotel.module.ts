import { Module } from '@nestjs/common';
import { ExotelController } from './exotel.controller';
import { ExotelService } from './exotel.service';
import { ExotelQuery } from './exotel.query';
import { PgService } from 'src/database/pg/pg.service';
import { UtilsModule } from 'src/utils/utils.module';
import { CommonModule } from 'src/common/common.module';
import { RedisModule } from 'src/database/redis/redis.module';

@Module({
  imports: [UtilsModule, CommonModule, RedisModule],
  controllers: [ExotelController],
  providers: [ExotelService, ExotelQuery, PgService],
  exports: [ExotelService],
})
export class ExotelModule {}
