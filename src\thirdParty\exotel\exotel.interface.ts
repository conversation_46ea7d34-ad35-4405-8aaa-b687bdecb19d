export interface exotelWebhookData {
  userId: string;
  callTo: string;
  loanId: number;
  callSId: string;
  callFrom: string;
  callStartAt: Date;
  userStage: number;
  assignedAdmin: number;
  webhookReceivedAt: Date;
  webhookResponse: object;
  callbackPreference: string;
}

export interface callbackReportQuery {
  page: string;
  startDate: Date;
  endDate: Date;
  isCalled: string;
  callbackSlot: string;
}
