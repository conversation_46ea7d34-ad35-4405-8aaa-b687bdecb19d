// Imports
import { Injectable } from '@nestjs/common';
import { CallKaroQuery } from './call.karo.query';
import { DateService } from 'src/utils/date.service';
import { NumberService } from 'src/utils/number.service';
import { ApiService } from 'src/utils/api.service';
import { callKaroHeaders, callKaroOutboundUrl } from 'src/constant/networks';
import { raiseParamMissing, raiseInternalError } from 'src/config/error';
import { isProd } from 'src/constant/global';
import { Env } from 'src/config/env';

@Injectable()
export class CallKaroService {
  constructor(
    private readonly num: NumberService,
    private readonly query: CallKaroQuery,
    private readonly dateService: DateService,
    private readonly api: ApiService,
  ) {}

  async callerData(reqData) {
    const userData: any = await this.query.dataForCallerData(reqData);

    if (userData.loan_amount) {
      userData.loan_amount = this.num.convertNumberToWords(
        +userData.loan_amount,
      );
    }
    if (userData.loan_tenure_in_days) {
      userData.loan_tenure_in_days = this.num.convertNumberToWords(
        +userData.loan_tenure_in_days,
      );
    }
    if (userData.emi_count) {
      userData.emi_count = this.num.convertNumberToWords(+userData.emi_count);
    }
    if (userData.emi_amount) {
      userData.emi_amount = this.num.convertNumberToWords(+userData.emi_amount);
    }
    if (userData.first_emi_date) {
      userData.first_emi_date = this.dateService.formatDateToLongMonth(
        userData.first_emi_date,
      );
      if (userData.interest_rate_per_anuum) {
        userData.interest_rate_per_anuum = this.num.convertFloatToWords(
          +userData.interest_rate_per_anuum,
        );
      }
    }

    return { userData };
  }

  async makeCall(body): Promise<any> {
    let to_number = body?.to_number;
    if (!to_number) return raiseParamMissing('to_number');

    if (!isProd) to_number = Env.qaTestNumbers.number1;
    // If to_number does not start with +91, prepend +91
    if (typeof to_number === 'string' && !to_number.startsWith('+91')) {
      to_number = '+91' + to_number;
    }

    const agent_id = body?.agent_id;
    if (!agent_id) return raiseParamMissing('agent_id');
    const metadata = body?.userData ?? {};
    if (!metadata) return raiseParamMissing('userData');

    const kCallKaroBody = {
      to_number,
      agent_id,
      metadata,
    };

    const response: any = await this.api.post(
      callKaroOutboundUrl,
      kCallKaroBody,
      callKaroHeaders,
    );
    if (response.statusCode === 500)
      return raiseInternalError('CallKaro API error');

    return response.response;
  }

  async webhook(body) {
    const callSid = body?.data?.callSid;
    if (!callSid) {
      console.error('Missing callSid in webhook body');
      return { success: false, error: 'Missing callSid' };
    }

    const bodyData = JSON.stringify(body);

    setTimeout(async () => {
      try {
        await this.query.updateCallKaro(callSid, bodyData);
      } catch (err) {
        console.error('Error updating CallKaro', { callSid, err });
      }
    }, 5000);

    return { success: true };
  }
}
