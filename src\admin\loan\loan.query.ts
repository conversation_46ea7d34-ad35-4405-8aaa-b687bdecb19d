// Imports
import { Op } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { raiseParamMissing } from 'src/config/error';
import { PgService } from 'src/database/pg/pg.service';
import { KYCEntity } from 'src/database/pg/entities/kyc.entity';
import { BankingEntity } from 'src/database/pg/entities/banking.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { CibilScoreEntity } from 'src/database/pg/entities/cibil.score.entity';
import { CallKaroEntity } from 'src/database/pg/entities/callKaro.entity';

@Injectable()
export class LoanQuery {
  constructor(private readonly pg: PgService) {}

  async syncLoanDetailsOptions(reqData): Promise<any> {
    const loanAttr = [
      'approvedDuration',
      'bankingId',
      'cibilId',
      'companyId',
      'completedLoan',
      'createdAt',
      'finalTypeOfDevice',
      'id',
      'interestRate',
      'loanCompletionDate',
      'loan_disbursement_date',
      'loanGmv',
      'manualVerificationAcceptId',
      'netApprovedAmount',
      'netEmiData',
      'userId',
    ];
    const loanOptions = {
      attributes: loanAttr,
      where: {
        loan_disbursement_date: {
          [Op.between]: [reqData.startDate, reqData.endDate],
        },
      },
    };

    const loanList = await this.pg.findAll(loanTransaction, loanOptions);

    // Non join query -> User data
    let userIds = loanList.map((el) => el.userId);
    userIds = [...new Set(userIds)];
    const userAttr = [
      'city',
      'email',
      'fullName',
      'gender',
      'id',
      'kycId',
      'phone',
      'state',
    ];
    const userOptions = {
      attributes: userAttr,
      where: { id: { [Op.in]: userIds } },
    };
    const userList = await this.pg.findAll(registeredUsers, userOptions);
    const userData = {};
    userList.forEach((el) => (userData[el.id] = el));

    // Non join query -> Banking data
    let bankingIds = loanList.map((el) => el.bankingId);
    bankingIds = [...new Set(bankingIds)];
    const bankingAttr = ['adminId', 'adminSalary', 'id', 'salary'];
    const bankingOptions = {
      attributes: bankingAttr,
      where: { id: { [Op.in]: bankingIds } },
    };
    const bankingList = await this.pg.findAll(BankingEntity, bankingOptions);
    const bankingData = {};
    bankingList.forEach((el) => (bankingData[el.id] = el));

    // Non join query -> Cibil data
    let cibilIds = loanList.map((el) => el.cibilId);
    cibilIds = [...new Set(cibilIds)];
    const cibilAttr = ['cibilScore', 'id', 'plScore', 'responsedata'];
    const cibilOptions = {
      attributes: cibilAttr,
      where: { id: { [Op.in]: cibilIds } },
    };
    const cibilList = await this.pg.findAll(CibilScoreEntity, cibilOptions);
    const cibilData = {};
    cibilList.forEach((el) => {
      cibilData[el.id] = el;
    });

    // Non join query -> KYC data
    let kycIds = userList.map((el) => el.kycId);
    kycIds = [...new Set(kycIds)];

    const kycOptions = {
      attributes: ['aadhaarDOB', 'aadhaarNo', 'aadhaarState', 'id'],
      where: { id: { [Op.in]: kycIds } },
    };
    const kycList = await this.pg.findAll(KYCEntity, kycOptions);
    const kycData = {};
    kycList.forEach((el) => {
      kycData[el.id] = el;
    });

    return { bankingData, cibilData, kycData, loanList, userData };
  }

  async syncEMIDetailsOptions(reqData) {
    const target_month = reqData.target_month;
    if (!target_month) {
      raiseParamMissing('target_month');
    }
    const loanIds = reqData.loanIds ?? [];
    const loanIdClause =
      loanIds.length > 0 ? `AND "loanId" IN (${loanIds.join(',')})` : '';

    let raw_query = `SELECT "emi_date", "payment_done_date", "loanId", "id", "emiNumber",
    "principalCovered", "interestCalculate", "fullPayPrincipal", "fullPayInterest", "partOfemi"
    FROM "EmiEntities" 
    WHERE TO_DATE("emi_date", 'YYYY-MM-DD') >= TO_DATE('${target_month}', 'Mon-YY')
    AND TO_DATE("emi_date", 'YYYY-MM-DD') <  (TO_DATE('${target_month}', 'Mon-YY') + INTERVAL '1 month') ${loanIdClause}`;
    const emiList = await this.pg.query(raw_query);
    const emiData = {};
    emiList.forEach((el) => {
      emiData[el.id] = el;
    });

    const emiIds = emiList.map((el) => el.id);
    raw_query = `SELECT "paidAmount", "principalAmount", "interestAmount", "completionDate",
    "emiId", "subscriptionDate", "id"
    FROM "TransactionEntities"
    WHERE "emiId" IN (${emiIds.join(',')}) AND "status" = 'COMPLETED'
    AND "type" != 'REFUND'
    ORDER BY "id" ASC`;
    const transList = await this.pg.query(raw_query);
    const transData = {};
    transList.forEach((el) => {
      if (!transData[el.emiId]) {
        transData[el.emiId] = [];
      }

      transData[el.emiId].push(el);
    });

    return { emiData, transData };
  }

  async dataForBulkSyncEmiDetails() {
    const raw_query = `SELECT 
          TO_CHAR(DATE_TRUNC('month', emi_date::date), 'Mon-YY') AS target_month
      FROM 
          "EmiEntities"
      WHERE
          emi_date IS NOT NULL
      GROUP BY 
          DATE_TRUNC('month', emi_date::date)
      ORDER BY 
          DATE_TRUNC('month', emi_date::date);`;

    return await this.pg.query(raw_query);
  }

  async getUsersAtSpecificStage(stage: number) {
    const userOptions = {
      attributes: ['id', 'lastLoanId', 'phone', 'stage', 'stageTime'],
      where: { stage },
    };

    return await this.pg.findAll(registeredUsers, userOptions);
  }

  async bulkCreateCallKaro(data: any[]) {
    return await this.pg.bulkCreate(CallKaroEntity, data);
  }
}
