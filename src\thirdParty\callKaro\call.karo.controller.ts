// Imports
import { CallKaroService } from './call.karo.service';
import { CallKaroGuard } from 'src/auth/third.party.guard';
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';

@Controller('thirdparty/callkaro')
export class CallKaroController {
  constructor(private readonly service: CallKaroService) {}

  @UseGuards(CallKaroGuard)
  @Get('callerData')
  async funCallerData(@Query() query) {
    return await this.service.callerData(query);
  }

  @UseGuards(CallKaroGuard)
  @Post('webhook')
  async funWebhook(@Body() body) {
    console.log('body', body);
    return await this.service.webhook(body);
  }

  @UseGuards(CallKaroGuard)
  @Post('makeCall')
  async makeCall(@Body() body) {
    return await this.service.makeCall(body);
  }
}
