// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { LeadQuery } from './lead.query';
import { LeadService } from './lead.service';
import { LeadController } from './lead.controller';
import { PgModule } from 'src/database/pg/pg.module';
import { UtilsModule } from 'src/utils/utils.module';
import { ReportModule } from '../report/report.module';
import { ReportService } from '../report/report.service';
import { ReportQueryService } from '../report/report.query';
import { CommonService } from 'src/common/common.services';
import { ExotelQuery } from 'src/thirdParty/exotel/exotel.query';
import { DataCodesQuery } from 'src/neighbours/data-codes/data.codes.query';
import { DataCodesService } from 'src/neighbours/data-codes/data.codes.service';

@Module({
  controllers: [LeadController],
  imports: [PgModule, UtilsModule, ReportModule],
  providers: [
    LeadService,
    LeadQuery,
    ReportService,
    ReportQueryService,
    CommonService,
    ExotelQuery,
    DataCodesQuery,
    DataCodesService,
  ],
})
export class LeadModule {}
