// Imports
import { EmiEntity } from './entities/emi.entity';
import { KYCEntity } from './entities/kyc.entity';
import { BankingEntity } from './entities/banking.entity';
import { loanTransaction } from './entities/loanTransaction';
import { registeredUsers } from './entities/registeredUsers';
import { CibilScoreEntity } from './entities/cibil.score.entity';
import { TransactionEntity } from './entities/transaction.entity';
import { PredictionEntity } from './entities/prediction.entity';
import { ExperianScoreEntity } from './entities/experian.entity';
import { MasterEntity } from './entities/master.entity';
import { TemplateEntity } from './entities/template.entity';
import { admin } from './entities/admin.entity';
import { employmentDetails } from './entities/employment.entity';
import { crmActivity } from './entities/crmActivity';
import { crmDisposition } from './entities/crmDisposition';
import { crmStatus } from './entities/crmStatus';
import { crmTitle } from './entities/crmTitle';
import { CrmReasonEntity } from './entities/crmReason.entity';
import { Department } from './entities/department';
import { exotelCallbackWaitlistEntity } from './entities/exotelCallback.entity';
import { BatchCibilDataEntity } from './entities/batchCibilData.entity';
import { BatchCibilFileTrackingEntity } from './entities/batchCibilFileTracking.entity';
import { ReportHistoryEntity } from './entities/reportHistory.entity';
import { CallKaroEntity } from './entities/callKaro.entity';
import { esignEntity } from './entities/eSign.entity';

export const PG_CORE_ENTITIES = [
  admin,
  BankingEntity,
  CibilScoreEntity,
  EmiEntity,
  KYCEntity,
  loanTransaction,
  MasterEntity,
  PredictionEntity,
  registeredUsers,
  TransactionEntity,
  admin,
  employmentDetails,
  crmActivity,
  crmDisposition,
  crmStatus,
  crmTitle,
  CrmReasonEntity,
  Department,
  ,
  BatchCibilDataEntity,
  BatchCibilFileTrackingEntity,
  esignEntity,
];

export const FIN_360_ENTITIES = [ExperianScoreEntity, CallKaroEntity];

export const IND_BANK_ENTITIES = [ReportHistoryEntity, TemplateEntity];

export const SMS_ENTITIES = [exotelCallbackWaitlistEntity];
