// Imports
import * as yaml from 'js-yaml';
import { readFileSync } from 'fs';
import { Logger } from '@nestjs/common';
import { kPathConfigYml } from 'src/constant/path';

export const YAML_CONFIG = getYamlConfig();

function getYamlConfig() {
  const yaml_config = yaml.load(readFileSync(kPathConfigYml, 'utf8')) as Record<
    string,
    any
  >;
  const logger = new Logger('ConfigLoader');
  logger.log('config.yml file is loaded successfully !');
  return yaml_config;
}

export const serverConfigs = YAML_CONFIG['server'] ?? {};

export const clickhouseConfigs =
  (YAML_CONFIG['database'] ?? {})['clickhouse'] ?? {};
export const postgresqlConfigs =
  (YAML_CONFIG['database'] ?? {})['postgresql'] ?? {};
export const redisConfigs = (YAML_CONFIG['database'] ?? {})['redis'] ?? {};

export const cryptConfigs = YAML_CONFIG['crypt'] ?? {};

// Neighbours
const neighbourConfigs = YAML_CONFIG['neighbours'] ?? {};
export const backendServiceConfigs = neighbourConfigs['backend-service'] ?? {};
export const dataCodesConfigs = neighbourConfigs['data-codes'] ?? {};
export const microAlertConfigs = neighbourConfigs['micro-alert'] ?? {};
export const transactionConfigs = neighbourConfigs['transactions'] ?? {};
export const trueShieldConfigs = neighbourConfigs['true-shield'] ?? {};

const thirdPartyConfigs = YAML_CONFIG['third-party'] ?? {};
export const callKaroConfigs = thirdPartyConfigs['callKaro'] ?? {};
export const experianConfigs = thirdPartyConfigs['experian'] ?? {};

//cloud
export const googleCloudConfig = (YAML_CONFIG['cloud'] ?? {})['google'] ?? {};
export const oracleCloudConfig = (YAML_CONFIG['cloud'] ?? {})['oracle'] ?? {};

//exotel
export const exotelConfigs = thirdPartyConfigs['exotel'] ?? {};

// System Configs
export const systemConfig = YAML_CONFIG['system'] ?? {};
export const businessHoursConfig = systemConfig['business-hours'] ?? {};

//Qa Test Numbers
export const qaTestNumbersConfig = YAML_CONFIG['qa-test-numbers'] ?? {};
