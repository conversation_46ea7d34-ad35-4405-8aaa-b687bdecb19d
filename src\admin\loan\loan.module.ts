// Imports
import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { <PERSON>an<PERSON><PERSON><PERSON> } from './loan.query';
import { LoanService } from './loan.service';
import { ApiService } from 'src/utils/api.service';
import { LoanController } from './loan.controller';
import { PgService } from 'src/database/pg/pg.service';
import { UtilsModule } from 'src/utils/utils.module';
import { CallKaroModule } from 'src/thirdparty/callKaro/call.karo.module';
import { RedisModule } from 'src/database/redis/redis.module';

@Module({
  controllers: [LoanController],
  imports: [UtilsModule, CallKaroModule, RedisModule],
  providers: [ApiService, LoanQuery, LoanService, PgService],
})
export class LoanModule {}
