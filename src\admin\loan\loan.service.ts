// Imports
import { LoanQuery } from './loan.query';
import { Injectable } from '@nestjs/common';
import { nDataCodes } from 'src/constant/networks';
import { ApiService } from 'src/utils/api.service';
import { raiseParamMissing } from 'src/config/error';
import { DateService } from 'src/utils/date.service';
import { isProd, kGlobalDateTrail } from 'src/constant/global';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { DATA_CODES_CLICKHOUSE_TABLE } from 'src/constant/objects';
import { getDataCodesAuth } from 'src/constant/auth';
import { CallKaroService } from 'src/thirdparty/callKaro/call.karo.service';
import { Env } from 'src/config/env';
import { RedisService } from 'src/database/redis/redis.service';

@Injectable()
export class LoanService {
  constructor(
    private readonly api: ApiService,
    private readonly dateService: DateService,
    private readonly query: LoanQuery,
    private readonly callKaroService: CallKaroService,
    private readonly redisService: RedisService,
  ) {}

  async syncLoanDetails(reqData): Promise<any> {
    const startDate = reqData.startDate;
    if (!startDate) raiseParamMissing('startDate');
    const endDate = reqData.endDate;
    if (!endDate) raiseParamMissing('endDate');
    reqData.startDate =
      new Date(startDate).toJSON().substring(0, 10) + kGlobalDateTrail;
    reqData.endDate =
      new Date(endDate).toJSON().substring(0, 10) + kGlobalDateTrail;
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const targetData = await this.query.syncLoanDetailsOptions(reqData);

    const finalizedList = [];
    for (let index = 0; index < targetData.loanList.length; index++) {
      try {
        const loanData = targetData.loanList[index];
        loanData.netApprovedAmount = +loanData.netApprovedAmount;

        const userData = targetData.userData[loanData.userId] ?? {};
        const bankingData = targetData.bankingData[loanData.bankingId] ?? {};
        const cibilData = targetData.cibilData[loanData.cibilId] ?? {};

        loanData.fullName = userData.fullName ?? '-';

        const loan_disbursement_date = new Date(
          loanData.loan_disbursement_date?.substring(0, 10),
        );

        // KYC data
        const kycData = targetData.kycData[userData.kycId] ?? {};
        const aadhaar_dob = this.dateService.aadhaarDateStrToDOBDate(
          kycData.aadhaarDOB,
        );
        const age = this.dateService.difference(
          loan_disbursement_date,
          aadhaar_dob,
          'Years',
        );

        // Raw details of the Emi Details
        const rawNetEMIData = loanData.netEmiData ?? ['{}'];
        const netEmiData = rawNetEMIData.map((el) => JSON.parse(el));
        netEmiData.forEach((el) => {
          el.dateTime = new Date(el.Date).getTime();
        });
        netEmiData.sort((a, b) => a - b);

        // Cibil calculation
        let cibil_response = cibilData.responsedata ?? '{}';
        const cibilScoreData =
          cibil_response.consumerCreditData[0]?.scores.find(
            (el) => el.scoreName == 'CIBILTUSC3',
          );
        const cibil_fetch_date = this.dateService.strToDate(
          cibilScoreData.scoreDate,
          'DDMMYYYY',
        );
        const cibil_enq_list =
          cibil_response.consumerCreditData[0]?.enquiries ?? [];
        let inq_last_3_days = 0;
        let inq_last_7_days = 0;
        let inq_last_30_days = 0;
        cibil_enq_list.forEach((el) => {
          const inq_date = this.dateService.strToDate(
            el.enquiryDate,
            'DDMMYYYY',
          );
          const diff_in_days = this.dateService.difference(
            inq_date,
            cibil_fetch_date,
          );
          if (diff_in_days <= 3) {
            inq_last_3_days++;
          }
          if (diff_in_days <= 7) {
            inq_last_7_days++;
          }
          if (diff_in_days <= 30) {
            inq_last_30_days++;
          }
        });
        const cached_cibil_days = this.dateService.difference(
          loan_disbursement_date,
          cibil_fetch_date,
        );
        let is_cached_cibil = false;
        if (cached_cibil_days > 7) {
          is_cached_cibil = true;
        } else {
          is_cached_cibil = cibil_response.internal_source == 'REDIS';
        }

        finalizedList.push({
          loanId: loanData.id,
          userId: loanData.userId,

          fullName: userData.fullName ?? '-',
          gender: userData.gender,
          email: userData.email ?? '-',
          phone: userData.phone ?? '-',
          typeOfDevice: +loanData?.finalTypeOfDevice,
          completedLoan: loanData?.completedLoan,

          age,
          aadhaar_hash: kycData.aadhaarNo,
          pan_number: '',
          aadhaar_state: kycData.aadhaarState,
          aadhaar_city: '',
          live_location_state: userData.state,
          live_location_city: userData.city,

          company_id: loanData.companyId,
          verified_salary: Math.floor(
            bankingData.adminSalary ?? bankingData.salary,
          ),
          salary_verified_admin_id: bankingData.adminId,
          loan_verified_admin_id: loanData.manualVerificationAcceptId,

          loan_applied_date_time: loanData.createdAt,
          loan_disbursement_date_time: '',
          loan_disbursement_date: loan_disbursement_date
            .toJSON()
            .substring(0, 10),
          netApprovedAmount: loanData.netApprovedAmount,
          interest_rate_per_day: loanData.interestRate,
          loan_duration_days: loanData.approvedDuration,

          cibil_score: cibilData.cibilScore,
          pl_score: cibilData.plScore,
          cibil_fetch_date: cibil_fetch_date.toJSON().substring(0, 10),
          is_cached_cibil,
          cached_cibil_days,
          is_mock_cibil: cibil_response.internal_source == 'REDIS',
          inq_last_30_days,
          inq_last_7_days,
          inq_last_3_days,

          eligibility_version: '',

          total_emis: netEmiData.length,
          first_emi_date: netEmiData[0].Date.substring(0, 10),
          last_emi_date: netEmiData[netEmiData.length - 1].Date.substring(
            0,
            10,
          ),

          loan_repayment_status: '',
          // loan_closure_date: loanData.loanCompletionDate?.substring(0, 10),
          cltv: loanData.loanGmv,
        });
      } catch (error) {
        console.log('error', error);
      }
    }

    // Purpose -> Debugging
    if (isReadOnly) {
      return { isReadOnly, count: finalizedList.length, rows: finalizedList };
    }

    const body = {
      db_type: 'duckDB',
      table_name: 'LoanDetails',
      objects: finalizedList,
    };
    return await this.api.post(nDataCodes.bulkWrites, body);
  }

  async syncEmiDetails(reqData) {
    const data = await this.query.syncEMIDetailsOptions(reqData);
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const today = this.dateService.getTodayGlobalDate();
    const todayTime = today.getTime();
    const payment_list = [];
    const emi_list = [];
    for (const emiId in data.emiData) {
      const emiData: EmiEntity = data.emiData[emiId];
      const emiDate = new Date(emiData.emi_date);
      const isEmiPaid =
        emiData.payment_done_date != null &&
        emiData.payment_done_date != undefined;

      let emi_status = 'UNKNOWN';
      if (!isEmiPaid) {
        if (emiDate.getTime() < todayTime) {
          emi_status = 'DEFAULT';
        } else if (emiDate.getTime() > todayTime) {
          emi_status = 'UPCOMING';
        }
      } else if (emiDate.getTime() <= todayTime) {
        const paidDate = this.dateService.getGlobalDate(
          new Date(emiData.payment_done_date),
        );
        if (paidDate.getTime() > emiDate.getTime()) {
          emi_status = 'DELAYED';
        } else if (paidDate.getTime() == emiDate.getTime()) {
          emi_status = 'ON_TIME';
        } else if (paidDate.getTime() < emiDate.getTime()) {
          emi_status = 'PRE_PAID';
        }
      }
      // Upcoming EMI paid as a pre paid
      else if (emiDate.getTime() > todayTime) {
        const paidDate = this.dateService.getGlobalDate(
          new Date(emiData.payment_done_date),
        );
        if (paidDate.getTime() < emiDate.getTime()) {
          emi_status = 'PRE_PAID';
        }
      }

      if (emi_status == 'UNKNOWN') {
        console.log({ emi_status });
      }

      const exp_principal = emiData.principalCovered;
      let exp_interest = emiData.interestCalculate;

      if (emi_status == 'PRE_PAID') {
        const fullPayPrincipal = emiData.fullPayPrincipal ?? 0;
        if (fullPayPrincipal > 0) {
          exp_interest = emiData.fullPayInterest ?? 0;
        }
      }

      emi_list.push({
        emi_id: emiData.id,
        emi_date: emiDate.toJSON().substring(0, 10),
        emi_paid_date: emiData.payment_done_date
          ? this.dateService
              .getGlobalDate(new Date(emiData.payment_done_date))
              .toJSON()
              .substring(0, 10)
          : null,
        emi_status,
        emi_number: emiData.emiNumber,
        part_of_emi: emiData.partOfemi,
        loanId: emiData.loanId,
        exp_principal,
        exp_interest,
      });

      let paid_principal = 0;
      const transList = data.transData[emiData.id] ?? [];
      for (let i = 0; i < transList.length; i++) {
        const transData = transList[i];

        let is_refund = false;
        if (
          paid_principal + transData.principalAmount > exp_principal &&
          transData.principalAmount != 0
        ) {
          is_refund = true;
        }

        payment_list.push({
          emi_id: emiData.id,
          transaction_id: transData.id.toString(),
          loan_id: emiData.loanId,
          payment_date: transData.completionDate.substring(0, 10),
          emi_date: emiDate.toJSON().substring(0, 10),
          principal_amount: (transData.principalAmount ?? 0).toString(),
          interest_amount: (transData.interestAmount ?? 0).toString(),
          is_refund: is_refund ? 1 : 0,
        });

        if (is_refund) {
          continue;
        }
        paid_principal += transData.principalAmount ?? 0;
      }

      if (emiData.fullPayPrincipal > 0) {
        const paidDate = this.dateService.getGlobalDate(
          new Date(emiData.payment_done_date),
        );
        let is_refund = false;
        if (emiData.fullPayPrincipal + paid_principal > exp_principal) {
          is_refund = true;
        }

        payment_list.push({
          emi_id: emiData.id,
          transaction_id: `f-${emiData.id}`,
          loan_id: emiData.loanId,
          payment_date: paidDate.toJSON().substring(0, 10),
          emi_date: emiDate.toJSON().substring(0, 10),
          principal_amount: (emiData.fullPayPrincipal ?? 0).toString(),
          interest_amount: (emiData.fullPayInterest ?? 0).toString(),
          is_refund: is_refund ? 1 : 0,
        });
      }
    }

    if (!isReadOnly) {
      // Bulk insert into clickHouse -> payment_details table
      await this.api.post(
        nDataCodes.bulkWrites,
        {
          db_type: 'clickHouse',
          table_name: DATA_CODES_CLICKHOUSE_TABLE.payment_details,
          objects: payment_list,
        },
        null,
        null,
        { headers: getDataCodesAuth() },
      );

      // Bulk insert into clickHouse -> emi_details table
      await this.api.post(
        nDataCodes.bulkWrites,
        {
          db_type: 'clickHouse',
          table_name: DATA_CODES_CLICKHOUSE_TABLE.emi_details,
          objects: emi_list,
        },
        null,
        null,
        { headers: getDataCodesAuth() },
      );
    }

    return {
      isReadOnly,
      count: payment_list.length,
      rows: payment_list,
      emi_rows: emi_list,
    };
  }

  async bulkSyncEmiDetails() {
    const targetList = await this.query.dataForBulkSyncEmiDetails();

    for (let index = 0; index < targetList.length; index++) {
      try {
        const data = targetList[index];
        await this.syncEmiDetails(data);
      } catch (error) {}
    }

    return {};
  }

  async makeCallsForStage(body) {
    const stage = +body?.stage;
    if (!stage) raiseParamMissing('stage');

    // Get current time
    const currentTime = new Date();
    const currentHour = currentTime.getHours();

    // Check if current time is between business hours
    const businessStartHour = Env.system.businessHours.startHour;
    const businessEndHour = Env.system.businessHours.endHour;
    if (currentHour < businessStartHour || currentHour >= businessEndHour) {
      return {
        message: `Outside business hours (${businessStartHour}:00 AM - ${businessEndHour}:00 PM)`,
        data: [],
      };
    }

    // Get users with stage = 10
    const users = await this.query.getUsersAtSpecificStage(stage);
    if (!users || users.length === 0) {
      return { message: `No users found at ${stage}`, data: [] };
    }

    const remainingSeconds = Math.floor(
      (new Date().setHours(24, 0, 0, 0) - Date.now()) / 1000,
    );

    const callResults = [];
    let callCount = 0;

    for (const user of users) {
      const targetDate = new Date('2025-07-31');
      const stageTime = new Date(user?.stageTime);

      // Check if stageTime exists
      if (!user?.stageTime || !user?.phone || stageTime <= targetDate) {
        continue;
      }
      // Calculate time difference in minutes
      const timeDifferenceMinutes = Math.floor(
        (currentTime.getTime() - stageTime.getTime()) / (1000 * 60),
      );

      const redisKey = `CALL_KARO_${stage}_${user?.id}`;
      const redisData = await this.redisService.getString(redisKey);
      // Only make call if time difference is > 10 minutes
      if (timeDifferenceMinutes > 10 && !redisData) {
        // If not in production, only make one call
        if (!isProd && callCount >= 1) {
          continue;
        }

        // Get caller data for the user
        const callerData = await this.callKaroService.callerData({
          number: user?.phone,
        });

        // Make the call
        const callResult = await this.callKaroService
          .makeCall({
            to_number: user?.phone,
            agent_id:
              Env.thirdParty.callKaro.agent_cust_support_loan_processing,
            userData: callerData?.userData,
          })
          .catch((err) => {
            console.log({ err });
          });

        await this.redisService.setString(
          redisKey,
          JSON.stringify(callResults ?? []),
          remainingSeconds,
        );

        callResults.push({
          userId: user?.id,
          loanId: user?.lastLoanId,
          date: currentTime,
          call_sid: callResult?.call_sid,
          stage: stage,
        });
        callCount++;
      }
    }

    await this.query.bulkCreateCallKaro(callResults).catch((err) => {
      console.log({ err });
    });

    return {
      message: 'call processing completed',
      totalUsers: users.length,
      callsMade: callResults.length,
      data: callResults,
    };
  }
}
