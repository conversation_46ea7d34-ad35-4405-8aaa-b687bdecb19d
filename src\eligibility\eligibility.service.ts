// Imports
import { nDataCodes } from 'src/constant/networks';
import { ApiService } from 'src/utils/api.service';
import { getDataCodesAuth } from 'src/constant/auth';
import { EligibilityQuery } from './eligibility.query';
import { HttpStatus, Injectable } from '@nestjs/common';

@Injectable()
export class EligibilityService {
  constructor(
    private readonly api: ApiService,
    private readonly query: EligibilityQuery,
  ) {}

  async checkPreApprovalStatus(reqData) {
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const target_data = await this.query.dataForPreApprovalStatus(reqData);
    if (isReadOnly) {
      return { success: true, data: target_data };
    }

    // Hit -> API
    const response = await this.api.post(
      nDataCodes.checkPreApproval,
      target_data,
      null,
      null,
      { headers: getDataCodesAuth() },
    );
    console.log('response', response);
    if (response.status == HttpStatus.OK) {
      return { success: true, data: response.response };
    }

    throw Error(`Api request failed -> ${nDataCodes.checkPreApproval}`);
  }
}
