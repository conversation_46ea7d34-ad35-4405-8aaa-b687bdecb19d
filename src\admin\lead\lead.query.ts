// Imports
import { Includeable, Op } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { decryptPhone } from 'src/utils/crypt';
import { EnumService } from 'src/utils/enum.service';
import { PgService } from 'src/database/pg/pg.service';
import { NumberService } from 'src/utils/number.service';
import { MasterEntity } from 'src/database/pg/entities/master.entity';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { ClickhouseService } from 'src/database/clickhouse/clickhouse.service';
import { DateService } from 'src/utils/date.service';
import { CibilScoreEntity } from 'src/database/pg/entities/cibil.score.entity';
import { employmentDetails } from 'src/database/pg/entities/employment.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import {
  finalStatuses,
  platforms,
  stageMapping,
  uiStageMapping,
} from 'src/constant/objects';
import { admin } from 'src/database/pg/entities/admin.entity';
import { BankingEntity } from 'src/database/pg/entities/banking.entity';
import { BatchCibilFileTrackingEntity } from 'src/database/pg/entities/batchCibilFileTracking.entity';
import { BatchCibilDataEntity } from 'src/database/pg/entities/batchCibilData.entity';
import { ExperianScoreEntity } from 'src/database/pg/entities/experian.entity';
import { Department } from 'src/database/pg/entities/department.entity';

@Injectable()
export class LeadQuery {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
    private readonly enumService: EnumService,
    private readonly numService: NumberService,
    private readonly clickhouse: ClickhouseService,
  ) {}

  async dataForHighLeadScore(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '4', '16', '17', '19', '21') AND "lead_score" > 15`;

    // Count only
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_id") AS "user_count"
          FROM "user_details" 
          
          ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw data
    else {
      let raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "lead_score" DESC, "unique_id" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      // Low performance query (Will change it later on)
      raw_query = `SELECT "userId", "cibilScore", "plScore"
      FROM "CibilScoreEntities"
      
      WHERE "userId" IN ('${userIds.join("','")}') AND "status" = '1'
      ORDER BY ID DESC`;
      const cibilList = await this.pg.query(raw_query);
      const cibilMap = {};
      cibilList.forEach((el) => {
        if (!cibilMap[el.userId]) {
          cibilMap[el.userId] = el;
        }
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const cibilData: CibilScoreEntity = cibilMap[data.user_id] ?? {};

        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Cibil Score': cibilData.cibilScore ?? '-',
          'PL Score': cibilData.plScore ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForHighLeadScoreUnTouched(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '3', '4', '5', '6', '7', '16', '17', '19', '21') 
      AND ("crm_details"."crm_id" = '' OR parseDateTimeBestEffortOrNull("crm_details"."created_at") < today())
      AND ("crm_counts"."crm_count" < 3 OR "crm_counts"."crm_count" IS NULL)`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_details"."user_id") AS "user_count"
      FROM "user_details" 

      LEFT JOIN "crm_details" 
        ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      LEFT JOIN (
        SELECT "user_id", count(*) AS "crm_count"
        FROM "crm_details"
        GROUP BY "user_id" ) AS "crm_counts"
        ON "crm_counts"."user_id" = "user_details"."user_id"

      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      let raw_query = `SELECT 
        "user_details"."user_id" AS "user_id", 
        "lead_score" AS "Lead Score",
        "stage", 
        "enc_phone", 
        "last_online_time" AS "Last Online Time", 
        "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" 
        ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      LEFT JOIN (
        SELECT "user_id", count(*) AS "crm_count"
        FROM "crm_details"
        GROUP BY "user_id" ) AS "crm_counts"
        ON "crm_counts"."user_id" = "user_details"."user_id"

      ${where_clause} 

      ORDER BY "last_online_time" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      // Low performance query (Will change it later on)
      raw_query = `SELECT "userId", "cibilScore", "plScore"
      FROM "CibilScoreEntities"
      
      WHERE "userId" IN ('${userIds.join("','")}') AND "status" = '1'
      ORDER BY ID DESC`;
      const cibilList = await this.pg.query(raw_query);
      const cibilMap = {};
      cibilList.forEach((el) => {
        if (!cibilMap[el.userId]) {
          cibilMap[el.userId] = el;
        }
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const cibilData: CibilScoreEntity = cibilMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Cibil Score': cibilData.cibilScore ?? '-',
          'PL Score': cibilData.plScore ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForHighLeadScoreNeverTouched(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '3', '4', '5', '6', '7', '16', '17', '19', '21') 
      AND ("crm_details"."crm_id" = '')`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_details"."user_id") AS "user_count"
      FROM "user_details" 
      
      LEFT JOIN "crm_details" 
      ON "crm_details"."crm_id" = "user_details"."recent_crm_id"
      
      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      let raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "last_online_time" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      // Low performance query (Will change it later on)
      raw_query = `SELECT "userId", "cibilScore", "plScore"
      FROM "CibilScoreEntities"
      
      WHERE "userId" IN ('${userIds.join("','")}') AND "status" = '1'
      ORDER BY ID DESC`;
      const cibilList = await this.pg.query(raw_query);
      const cibilMap = {};
      cibilList.forEach((el) => {
        if (!cibilMap[el.userId]) {
          cibilMap[el.userId] = el;
        }
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const cibilData: CibilScoreEntity = cibilMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Cibil Score': cibilData.cibilScore ?? '-',
          'PL Score': cibilData.plScore ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForLeftTheAppFewMinsAgo(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause}
      AND "stage" NOT IN ('1', '2', '4', '5', '6', '7', '16', '17', '19', '21') 
      AND parseDateTimeBestEffortOrNull("last_online_time") >= now() - interval 25 minute
      AND parseDateTimeBestEffortOrNull("last_online_time") <= now() - interval 5 minute`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_id") AS "user_count"
      FROM "user_details" 
      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }

    // Raw Data
    else {
      let raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "last_online_time" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      // Low performance query (Will change it later on)
      raw_query = `SELECT "userId", "cibilScore", "plScore"
      FROM "CibilScoreEntities"
      
      WHERE "userId" IN ('${userIds.join("','")}') AND "status" = '1'
      ORDER BY ID DESC`;
      const cibilList = await this.pg.query(raw_query);
      const cibilMap = {};
      cibilList.forEach((el) => {
        if (!cibilMap[el.userId]) {
          cibilMap[el.userId] = el;
        }
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const cibilData: CibilScoreEntity = cibilMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Cibil Score': cibilData.cibilScore ?? '-',
          'PL Score': cibilData.plScore ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForNthTimeAttemptRemain(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `AND "recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `AND "recent_cse_id" IS NOT NULL`;
    }

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `
      WITH
        -- Step 1: Filter today's CRM in IST
        crm_today AS (
            SELECT
                crm_id,
                user_id,
                remark,
                toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') AS created_ist
            FROM crm_details
            WHERE
                toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') >= toStartOfDay(now(), 'Asia/Kolkata')
                AND toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') < toStartOfDay(now() + INTERVAL 1 DAY, 'Asia/Kolkata')
        ),

        -- Step 2: Find user_ids with exactly N CRM entries today
        valid_crm_users AS ( SELECT user_id
            FROM crm_today
            GROUP BY user_id HAVING count() = ${reqData.crm_attempt}
        )

      -- Step 3: Count users with valid CRM today
      SELECT count(*) AS user_count
      FROM user_details u
      INNER JOIN crm_today c ON c.crm_id = u.recent_crm_id
      INNER JOIN valid_crm_users v ON v.user_id = u.user_id
      WHERE
          u.recent_cse_id IS NOT NULL
          AND u.stage NOT IN ('1', '2', '4', '5', '6', '7', '16', '17', '19', '21') 
          ${admin_where_clause};`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      let raw_query = `WITH
          -- Step 1: Filter today's CRM in IST
          crm_today AS (
              SELECT
                  crm_id,
                  user_id,
                  remark,
                  toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') AS created_ist
              FROM crm_details
              WHERE
                  created_ist >= toStartOfDay(now(), 'Asia/Kolkata')
                  AND created_ist < toStartOfDay(now() + INTERVAL 1 DAY, 'Asia/Kolkata')),
          
          -- Step 2: Find user_ids with exactly one CRM today
          valid_crm_users AS (
              SELECT user_id FROM crm_today
              GROUP BY user_id HAVING count() = ${reqData.crm_attempt} )
          
      -- Step 3: Join back to get full info for users with exactly one CRM today
      SELECT
          u.user_id AS "user_id",
          u.lead_score AS "Lead Score",
          u.stage,
          u.enc_phone,
          u.last_online_time AS "Last Online Time",
          c.remark AS "Last CRM"
      FROM user_details u
      INNER JOIN crm_today c ON c.crm_id = u.recent_crm_id
      INNER JOIN valid_crm_users v ON v.user_id = u.user_id
      WHERE
          u.recent_cse_id IS NOT NULL
          AND u.stage NOT IN ('1', '2', '4', '16', '17', '19', '21')
          ${admin_where_clause}
      ORDER BY u.lead_score DESC, u.unique_id DESC`;

      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      // Low performance query (Will change it later on)
      raw_query = `SELECT "userId", "cibilScore", "plScore"
      FROM "CibilScoreEntities"
      
      WHERE "userId" IN ('${userIds.join("','")}') AND "status" = '1'
      ORDER BY ID DESC`;
      const cibilList = await this.pg.query(raw_query);
      const cibilMap = {};
      cibilList.forEach((el) => {
        if (!cibilMap[el.userId]) {
          cibilMap[el.userId] = el;
        }
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const cibilData: CibilScoreEntity = cibilMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Cibil Score': cibilData.cibilScore ?? '-',
          'PL Score': cibilData.plScore ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  //#region getStageWiseLeadData
  // Public method to fetch user details grouped by stage
  async getStageWiseLeadData(reqData) {
    // Variables
    const adminId = reqData?.adminId ?? -1;
    let stage = reqData?.stage;
    let status = reqData?.status;
    let subStage = reqData?.subStage;
    const crmRemark = reqData?.crmRemark ?? null;
    const startDate = reqData?.startDate;
    const endDate = reqData?.endDate;
    let dateRange: any = this.dateService.getUTCDateRange(startDate, endDate);

    // #1 Clickhouse Query
    let stageAttribute = '';
    if (stage == 0 || !stage) stageAttribute = '';
    else if (stage == '1')
      stageAttribute = `AND (user_details.stage is NULL OR user_details.stage = 1)`;
    else stageAttribute = `AND user_details.stage = '${stage}'`;
    let stageTimeAttribute = `AND user_details.stage_time BETWEEN '${dateRange.fromDate}' AND '${dateRange.endDate}'`;
    if (stage == '1')
      stageTimeAttribute = `AND (user_details.stage_time BETWEEN '${dateRange.fromDate}' AND '${dateRange.endDate}' OR user_details.stage_time IS NULL)`;

    const clickhouse_query = `
      SELECT user_id, stage, lead_score,created_at,stage_time,recent_cse_id,app_type,
        crm_details.remark, crm_details.created_at, crm_details.admin_id
      FROM user_details
      LEFT JOIN "crm_details" ON "crm_details"."user_id" = "user_details"."user_id"
      WHERE 1=1
      ${
        adminId && adminId != '-1'
          ? `AND recent_cse_id = '${adminId}'`
          : adminId == '-1'
            ? 'AND recent_cse_id IS NOT NULL'
            : ''
      }
      AND user_details.stage NOT IN (9,17,18,19,21,20)
      ${stageAttribute}
      ${stageTimeAttribute}
      ORDER BY stage_time DESC
    `;
    const userData: any = await this.clickhouse.injectQuery(clickhouse_query);
    const groupedResult = [];
    const allCrms = [];

    // #2 User Map
    const userMap: Record<string, any> = {};
    userData.forEach((record) => {
      const userId = record.user_id;
      if (record.stage == null) record.stage = 1;
      // Create user entry if not already present
      if (!userMap[userId]) {
        userMap[userId] = {
          user_id: record.user_id,
          stage: record.stage,
          lead_score: record.lead_score,
          created_at: record.created_at,
          recent_cse_id: record.recent_cse_id,
          app_type: record.app_type,
        };
        groupedResult.push(userMap[userId]);
      }
      // Extract CRM entry if present
      if (
        record['crm_details.created_at'] &&
        new Date(record['crm_details.created_at']) >=
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) &&
        (record.remark || record['crm_details.created_at'] || record.admin_id)
      ) {
        allCrms.push({
          user_id: record.user_id,
          remark: record.remark,
          created_at: record['crm_details.created_at'],
          admin_id: record.admin_id,
        });
      }
    });

    // #3 Group UserIds by Stage
    const stageUsers: Record<string, string[]> = {};
    const userDataMap: Record<string, any> = {};

    groupedResult.forEach((record: any) => {
      const stageName =
        reqData.count === 'true'
          ? uiStageMapping[record.stage]
          : stageMapping[record.stage];
      if (stageName) {
        if (!stageUsers[stageName]) stageUsers[stageName] = [];
        stageUsers[stageName].push(record.user_id);
      }
      userDataMap[record.user_id] = record;
    });

    // #4 Count Only -> When We Need Only Count as Reponse
    if (reqData.count === 'true')
      return this.getStageWiseLeadDataCount({
        stage,
        reqData,
        stageUsers,
        uiStageMapping,
        stageMapping,
      });
    // #5 Data Only -> When We Need Data as Reponse
    else
      return await this.getStageWiseLeadFullData({
        stage,
        reqData,
        stageUsers,
        stageMapping,
        groupedResult,
        dateRange,
        allCrms,
        userDataMap,
        crmRemark,
        subStage,
        status,
      });
  }
  //#endregion

  //#region Get Stage Wise Lead Data Count
  getStageWiseLeadDataCount({
    stage,
    reqData,
    stageUsers,
    uiStageMapping,
    stageMapping,
  }) {
    const stageCounts: any = {};
    // If a specific stage is requested, only return that stage
    if (stage) {
      const stageName =
        reqData.count === 'true' ? uiStageMapping[stage] : stageMapping[stage];

      if (stageName) {
        const count = stageUsers[stageName]?.length || 0;
        return {
          data: [
            {
              title: stageName,
              count: count,
              percentage: 100,
            },
          ],
          total: count,
        };
      }
    }

    // Initialize counts for all UI stages
    Object.values(
      reqData.count === 'true' ? uiStageMapping : stageMapping,
    ).forEach((stageName: any) => {
      stageCounts[stageName] = 0;
    });

    // Count users for each stage
    Object.keys(stageUsers).forEach((stageName) => {
      stageCounts[stageName] = stageUsers[stageName].length;
    });

    const total: any = Object.values(stageCounts).reduce(
      (sum: number, count: any) => sum + Number(count || 0),
      0,
    );

    const data = Object.entries(uiStageMapping).map(([_key, title]: any) => ({
      id: Object.entries(uiStageMapping).find(
        ([_, value]) => value === title,
      )?.[0],
      title,
      count: stageCounts[title] ?? 0,
      percentage:
        total > 0
          ? +((Number(stageCounts[title] ?? 0) * 100) / total).toFixed(2)
          : 0,
    }));
    return { data, total };
  }
  //#endregion

  //#region Get Stage Wise Lead Full Data
  async getStageWiseLeadFullData({
    stage,
    reqData,
    stageUsers,
    stageMapping,
    groupedResult,
    dateRange,
    allCrms,
    userDataMap,
    crmRemark,
    subStage,
    status,
  }) {
    // Fetch User Details for all userIds
    const allUserIds = Array.from(
      new Set(groupedResult.map((r: any) => r.user_id)),
    );
    dateRange = { [Op.gte]: dateRange.fromDate, [Op.lte]: dateRange.endDate };
    let userDetails: Record<
      string,
      {
        fullName: string;
        email: string;
        phone: string;
        completedLoans: number;
        otherInfo?: any;
        empData: any;
        typeOfDevice: string;
        crmData: any;
        loanId?: number;
        masterData: any;
        cseCrmData: any;
        analystCrmData: any;
        adminData: any;
        loanData: any;
        bankingData: any;
      }
    > = {};
    let cibilMap = {};
    let fileMap = {};
    let experianMap = {};
    let adminMap = {};
    // #1 If UserIds are Present
    if (allUserIds.length > 0) {
      // Employment Data
      const employmentInc: Includeable = {
        model: employmentDetails,
        attributes: ['companyName'],
      };
      // Master Data
      const masterInc: Includeable = {
        model: MasterEntity,
        required: true,
      };
      masterInc.attributes = [
        'userId',
        'otherInfo',
        'loanId',
        'assignedCSE',
        'status',
        'rejection',
      ];
      let userAttributes = [
        'id',
        'fullName',
        'email',
        'phone',
        'completedLoans',
        'typeOfDevice',
        'lastCrm',
      ];
      // Adding Extra Columns When We Need Download Data
      if (reqData.download === 'true') {
        // Fetching Bureau Data
        //#1 CIBIL Data
        let cibilData = await this.pg.findAll(BatchCibilDataEntity, {
          attributes: ['id', 'userId', 'cibilScore', 'plScore', 'fileId'],
          where: { userId: allUserIds },
          order: [['id', 'DESC']],
        });
        let fileIds = [...new Set(cibilData.map((el) => el.fileId))];
        let batchCibilFileList = await this.pg.findAll(
          BatchCibilFileTrackingEntity,
          {
            attributes: ['id', 'batchOutputDate'],
            where: { id: fileIds },
          },
        );
        cibilData.forEach((el) => {
          cibilMap[el.userId] = el;
        });
        batchCibilFileList.forEach((el) => {
          fileMap[el.id] = el;
        });

        // #2 Experian Data
        const experianData = await this.pg.findAll(ExperianScoreEntity, {
          attributes: [
            'id',
            'userId',
            'experianScore',
            'overdueAccounts',
            'overdueAmount',
            'inquiryPast30Days',
          ],
          where: { userId: allUserIds },
        });
        experianData.forEach((el) => {
          experianMap[el.userId] = el;
        });
      }

      // User Data
      const users = await this.pg.findAll(registeredUsers, {
        attributes: userAttributes,
        include: [masterInc, employmentInc],
        where: { id: allUserIds },
        raw: true,
      });

      // Get loan IDs and admin IDs from the results
      const loanIds = Array.from(
        new Set(users.map((el) => el.masterData?.loanId).filter(Boolean)),
      );
      const allAdminIds = Array.from(
        new Set((allCrms as any[]).map((r: any) => r.admin_id).filter(Boolean)),
      );

      let loanWhere: any = { id: loanIds };
      const [actualLoanData, cibilData] = await Promise.all([
        // Loan Data
        loanIds.length > 0
          ? this.pg.findAll(loanTransaction, {
              attributes: [
                'id',
                'netApprovedAmount',
                'createdAt',
                'userId',
                'bankingId',
                'assignTo',
              ],
              where: loanWhere,
            })
          : [],
        // Cibil Data
        loanIds.length > 0
          ? this.pg.findAll(CibilScoreEntity, {
              attributes: ['userId', 'cibilScore', 'plScore'],
              where: { loanId: loanIds },
              order: [['id', 'DESC']],
            })
          : [],
      ]);
      cibilData.forEach((el) => {
        cibilMap[el.userId] = el;
      });

      // CA -> Add assignTo IDs from loanTransaction to allAdminIds
      const caIds = Array.from(
        new Set(actualLoanData.map((el) => el.assignTo).filter(Boolean)),
      );
      // CSE -> Add assignedCSE IDs from user data
      const cseIds = Array.from(
        new Set(groupedResult.map((r: any) => r.recent_cse_id)),
      );
      const updatedAdminIds = [
        ...new Set([...allAdminIds, ...caIds, ...cseIds]),
      ];
      // Now fetch admin data with all admin IDs (including assignTo IDs)
      const actualAdminData =
        updatedAdminIds.length > 0
          ? await this.pg.findAll(admin, {
              attributes: ['id', 'fullName', 'departmentId'],
              where: { id: updatedAdminIds },
            })
          : [];

      // Get banking IDs and banking Data
      let bankingData: any = [];
      const bankingIds = Array.from(
        new Set(actualLoanData.map((el) => el.bankingId).filter(Boolean)),
      );
      let bankWhere: any = { id: bankingIds };
      bankingData =
        bankingIds.length > 0
          ? await this.pg.findAll(BankingEntity, {
              attributes: [
                'aaDataStatus',
                'salary',
                'userId',
                'dataOfMonth',
                'consentId',
              ],
              where: bankWhere,
            })
          : [];

      // Get department IDs and department Data
      const departmentIds = Array.from(
        new Set(actualAdminData.map((el) => el.departmentId).filter(Boolean)),
      );
      const actualDepartmentData =
        departmentIds.length > 0
          ? await this.pg.findAll(Department, {
              attributes: ['id', 'department'],
              where: { id: departmentIds },
            })
          : [];

      // Create lookup maps
      adminMap = {};
      actualAdminData.forEach((el: any) => {
        adminMap[el.id] = el;
      });
      const departmentMap = {};
      (actualDepartmentData as any[]).forEach((el) => {
        departmentMap[el.id] = el;
      });
      const loanMap = {};
      actualLoanData.forEach((el) => {
        loanMap[el.userId] = el;
      });
      const bankingMap = {};
      (bankingData as any[]).forEach((el) => {
        bankingMap[el.userId] = el;
      });

      // Pre-process CRM data
      const crmByUser = {};
      (allCrms as any[]).forEach((crm: any) => {
        if (!crmByUser[crm.user_id]) {
          crmByUser[crm.user_id] = { cse: [], analyst: [] };
        }
        const admin = adminMap[crm.admin_id];
        if (admin?.departmentId === 1) {
          crmByUser[crm.user_id].cse.push(crm);
        } else if (admin?.departmentId === 2) {
          crmByUser[crm.user_id].analyst.push(crm);
        }
      });

      // Sort CRM data by date (most recent first)
      Object.keys(crmByUser).forEach((userId) => {
        crmByUser[userId].cse.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );
        crmByUser[userId].analyst.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );
      });

      // Preparing User Data - Single pass
      users.forEach((u: any) => {
        const userCrm = crmByUser[u.id] || { cse: [], analyst: [] };
        const loan = loanMap[u.id];
        const banking = bankingMap[u.id];

        userDetails[u.id] = {
          fullName: u.fullName,
          email: u.email,
          phone: u.phone,
          completedLoans: u.completedLoans,
          typeOfDevice: u?.typeOfDevice,
          crmData: u.lastCrm,
          masterData: u.masterData,
          empData: u.employmentData,
          cseCrmData: userCrm.cse[0] || null,
          analystCrmData: userCrm.analyst[0] || null,
          adminData: actualAdminData,
          loanData: loan,
          bankingData: banking,
        };
      });
    }

    // Build response grouped by stage
    const result: any = {};
    let missingMonthUsers = [];
    let totalAppliedAmount = 0;
    Object.keys(stageUsers).forEach((stage) => {
      result[stage] = stageUsers[stage].map((userId) => {
        // Master Data
        const masterData = userDetails[userId]?.masterData;
        // Banking Data
        const bankingData = userDetails[userId]?.bankingData;
        const aaResponse =
          bankingData?.aaDataStatus == 1
            ? 'Pending'
            : bankingData?.aaDataStatus == 2
              ? 'Failed'
              : bankingData?.aaDataStatus == 3
                ? 'Fetched'
                : '';
        const missingMonth = bankingData?.dataOfMonth?.includes('false');
        if (missingMonth) missingMonthUsers.push(userId);
        // Loan Data
        const loanData = userDetails[userId]?.loanData;
        // Applied Amount
        const netApprovedAmount = loanData?.netApprovedAmount;
        const loanCreatedAt = loanData?.createdAt;
        // Status Data
        const statusObj = masterData?.status;
        // Getting Status and Reason
        const checkStatus = this.getUserStatus(stage, statusObj);
        // CSE-CRM Data
        const cseCrmData = userDetails[userId]?.cseCrmData;
        // Analyst-CRM Data
        const analystCrmData = userDetails[userId]?.analystCrmData;
        // Adding Total Amount Loan Applied For
        if (
          status &&
          finalStatuses[status] == checkStatus &&
          !isNaN(Number(netApprovedAmount))
        )
          totalAppliedAmount += Number(netApprovedAmount);
        else if (
          !status &&
          netApprovedAmount &&
          !isNaN(Number(netApprovedAmount))
        )
          totalAppliedAmount += Number(netApprovedAmount);
        const empInfo = masterData?.otherInfo?.employmentInfo
          ? (masterData?.otherInfo?.employmentInfo)
              .split('-')
              .map(
                (word) =>
                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
              )
              .join('-')
          : '-';

        // Prepare Object to Return Response
        let obj = {
          user_id: userId,
          appType: userDataMap[userId]?.app_type ?? '-',
          'Full Name': userDetails[userId]?.fullName ?? '-',
          'Loan Id': masterData?.loanId ?? '-',
          'Mobile Number': userDetails[userId]?.phone ?? '-',
          Email: userDetails[userId]?.email ?? '-',
          'Applied Amount': netApprovedAmount ?? 0,
          'Completed Loans': userDetails[userId]?.completedLoans ?? '-',
          Salary: masterData?.otherInfo?.salaryInfo ?? '-',
          'CIBIL Score': cibilMap[userId]?.cibilScore ?? '-',
          'PL Score': cibilMap[userId]?.plScore ?? '-',
          'Lead Score': userDataMap[userId]?.lead_score ?? 0,
          'Assigned CSE':
            adminMap[userDataMap[userId]?.recent_cse_id]?.fullName ?? '-',
          'Assigned CA': adminMap[loanData?.assignTo]?.fullName ?? '-',
          'Action Track':
            this.enumService.getUserStage(userDataMap[userId]?.stage) ?? '-',
          // CIBIL Data
          'Batch CIBIL': '',
          'Batch PL': '',
          'Batch Date': '',
          // Experian Data
          'Experian Score': '',
          'Exp. Overdue Accounts': '',
          'Exp. Overdue Amount': '',
          'Exp. Inquiries(30D)': '',
          'Loan Created At': '',
          'Reg. Date': '',
          'AA Response': aaResponse,
          'Employment  Information': empInfo,
          Platform: platforms[userDetails[userId]?.typeOfDevice] ?? '-',
          'Company Name': userDetails[userId]?.empData?.companyName ?? '-',

          // CRM Data (CSE & Analyst)
          'CSE-CRM Date': cseCrmData?.created_at
            ? this.dateService.readableDate(cseCrmData?.created_at)
            : '-',
          'CSE-CRM Created By': adminMap[cseCrmData?.admin_id]?.fullName ?? '-',
          'CSE-CRM Remark': cseCrmData?.remark ?? '-',
          'CA-CRM Date': analystCrmData?.created_at
            ? this.dateService.readableDate(analystCrmData?.created_at)
            : '-',
          'CA-CRM Created By':
            adminMap[analystCrmData?.admin_id]?.fullName ?? '-',
          'CA-CRM Remark': analystCrmData?.remark ?? '-',
          'Verified Salary': '',
          Status: checkStatus ?? '-',
        };

        // Adding Extra Columns When We Need Download Data
        if (reqData.download === 'true') {
          // #CIBIL Data
          const cibilData = cibilMap[userId];
          const fileData = fileMap[cibilData?.fileId];
          obj['Batch CIBIL'] = cibilData?.cibilScore ?? '-';
          obj['Batch PL'] = cibilData?.plScore ?? '-';
          obj['Batch Date'] = fileData?.batchOutputDate
            ? this.dateService.dateToReadableFormat(
                new Date(fileData?.batchOutputDate),
              )?.readableStr
            : '-';

          // #Experian Data
          const experianData = experianMap[userId];
          obj['Experian Score'] = experianData?.experianScore ?? '-';
          obj['Exp. Overdue Accounts'] = experianData?.overdueAccounts ?? '-';
          obj['Exp. Overdue Amount'] = experianData?.overdueAmount ?? '-';
          obj['Exp. Inquiries(30D)'] = experianData?.inquiryPast30Days ?? '-';

          obj['Reg. Date'] = userDataMap[userId]?.created_at
            ? this.dateService.readableDate(userDataMap[userId]?.created_at)
            : '-';
          obj['Loan Created At'] = loanCreatedAt
            ? this.dateService.readableDate(loanCreatedAt)
            : '-';
          obj['Verified Salary'] = bankingData?.salary ?? '-';
          delete obj.appType;
        }
        // Only remove empty string values, not all empty values
        else {
          Object.keys(obj).forEach((key) => {
            if (obj[key] === '') delete obj[key];
          });
        }
        return obj;
      });
    });

    // Applinig Filters on Result
    // For Sub Stage Filter
    if (status) {
      let statusType =
        status == '1' ? 'Under Verfication' : status == '2' ? 'Pending' : '';
      if (stage) {
        result[stageMapping[stage]] = result[stageMapping[stage]]?.filter(
          (el) => el.Status == statusType,
        );
      } else {
        Object.keys(result).forEach((stage) => {
          result[stage] = result[stage]?.filter(
            (el) => el.Status == statusType,
          );
        });
      }
      totalAppliedAmount = 0;
      Object.keys(result).forEach((stage) => {
        totalAppliedAmount += result[stage]?.reduce(
          (acc, el) => acc + Number(el['Applied Amount']),
          0,
        );
      });
    }
    // For CRM Remark Filter
    if (crmRemark == '1') {
      totalAppliedAmount = 0;
      Object.keys(result).forEach((stage) => {
        result[stage] = result[stage]?.filter(
          (el) => el['CSE-CRM Remark'] != '-' || el['CA-CRM Remark'] != '-',
        );
        totalAppliedAmount += result[stage]?.reduce(
          (acc, el) => acc + Number(el['Applied Amount']),
          0,
        );
      });
    } else if (crmRemark == '2') {
      totalAppliedAmount = 0;
      Object.keys(result).forEach((stage) => {
        result[stage] = result[stage]?.filter(
          (el) => el['CSE-CRM Remark'] == '-' && el['CA-CRM Remark'] == '-',
        );
        totalAppliedAmount += result[stage]?.reduce(
          (acc, el) => acc + Number(el['Applied Amount']),
          0,
        );
      });
    }
    // Filter for Sub Stage // Only for Banking Stage
    if (stage == '8' && subStage == '1') {
      totalAppliedAmount = 0;
      Object.keys(result).forEach((stage) => {
        result[stage] = result[stage]?.filter(
          (el) => el['AA Response'] == 'Pending',
        );
        totalAppliedAmount += result[stage]?.reduce(
          (acc, el) => acc + Number(el['Applied Amount']),
          0,
        );
      });
    } else if (stage == '8' && subStage == '2') {
      totalAppliedAmount = 0;
      Object.keys(result).forEach((stage) => {
        result[stage] = result[stage]?.filter((el) =>
          missingMonthUsers.includes(el.user_id),
        );
        totalAppliedAmount += result[stage]?.reduce(
          (acc, el) => acc + Number(el['Applied Amount']),
          0,
        );
      });
    }

    // Return Data for Specific Stage
    if (stage) {
      let response = result[stageMapping[stage]] ?? [];
      return {
        count: response.length,
        totalAppliedAmount,
        rows: response,
      };
    }
    // Return Data for All Stages
    else if (!stage) {
      let arr = [];
      Object.keys(result).forEach((stage) => {
        arr.push(...result[stage]);
      });
      return {
        count: arr.length,
        totalAppliedAmount,
        rows: arr,
      };
    }
    return { result, totalAppliedAmount };
  }
  //#endregion

  //#region Get User Status
  getUserStatus(stage, statusObj) {
    const mappedStageToStatusKey: Record<string, string> = {
      PHONE_VERIFICATION: 'phone',
      BASIC_DETAILS: 'basic',
      SELFIE: 'selfie',
      PIN: 'pin',
      AADHAAR: 'aadhaar',
      EMPLOYMENT: 'company',
      BANKING: 'bank',
      PAN: 'pan',
      FINAL_VERIFICATION: 'eligibility',
      MANDATE: 'permission',
      ESIGN: 'esign',
      DISBURSEMENT: 'disbursement',
      LOAN_ACCEPT: 'loan',
      REAPPLY: 'reapply',
    };

    if (!statusObj) return '-';
    stage = mappedStageToStatusKey[stage];
    let status =
      statusObj[stage] == -1
        ? finalStatuses['1']
        : statusObj[stage] == 0 || statusObj[stage] == 5
          ? finalStatuses['2']
          : finalStatuses['0'];
    if (stage == 'bank' && statusObj[stage] == 4) status = finalStatuses['1'];
    else if (stage == 'loan' && statusObj[stage] == 1)
      status = finalStatuses['1'];
    else if (
      (stage == 'eMandate' || stage == 'permission') &&
      (statusObj[stage] == 1 || statusObj[stage] == -1)
    )
      status = finalStatuses['1'];
    else if (stage == 'reapply') status = finalStatuses['1'];
    else if (stage == 'esign') status = finalStatuses['1'];
    else if (stage == 'disbursement' && statusObj[stage] == 0)
      status = finalStatuses['1'];
    return status;
  }
  //#endregion
}
