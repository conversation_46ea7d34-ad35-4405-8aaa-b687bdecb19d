import { Injectable } from '@nestjs/common';
import { ExotelQuery } from './exotel.query';
import { raiseParamMissing } from 'src/config/error';
import {
  kActiveLoan,
  kExotelRedisKey,
  kRejectLoan,
} from 'src/constant/strings';
import { exotelCallbackWaitlistEntity } from 'src/database/pg/entities/exotelCallback.entity';
import { PgService } from 'src/database/pg/pg.service';
import { kSupportAdminIds, NUMBERS } from 'src/constant/objects';
import { Env } from 'src/config/env';
import { UtilsService } from 'src/utils/utils.service';
import { CommonService } from 'src/common/common.services';
import { exotelWebhookData } from './exotel.interface';
import { ApiService } from 'src/utils/api.service';
import { exotelHeaders, exotelUserUrl } from 'src/constant/networks';
import { RedisService } from 'src/database/redis/redis.service';

@Injectable()
export class ExotelService {
  constructor(
    private readonly query: ExotelQuery,
    private readonly pg: PgService,
    private readonly utils: UtilsService,
    private readonly commonService: CommonService,
    private readonly apiService: ApiService,
    private readonly redisService: RedisService,
  ) {}

  async scheduleCallbackHandler(reqData) {
    if (!reqData?.CallFrom) raiseParamMissing('Caller Phone');
    const callerNum = this.utils.removeLeadingZero(reqData?.CallFrom);
    const data = await this.query.getCompleteUserDetails(callerNum);
    const loanData = data?.loanData;
    const userData = data?.userData;
    const masterData = userData?.masterData;

    let adminId;
    if (loanData?.loanStatus != kActiveLoan && masterData?.assignedCSE)
      adminId = masterData?.assignedCSE;
    else if (loanData?.loanStatus == kActiveLoan) {
      if (loanData?.followerId) {
        adminId = loanData?.followerId;
      } else adminId = masterData?.assignedCSE;
    }
    const callbackPreference = JSON.parse(reqData?.digits);

    const redisKey = `${kExotelRedisKey}${userData?.id ?? '-'}_${reqData?.CallSid}`;
    const setAtRedis = await this.redisService.setIfNotExistsWithNX(
      redisKey,
      NUMBERS.ONE_HOURS_IN_SECONDS,
    );
    if (!setAtRedis) return;

    const payload: exotelWebhookData = {
      userId: userData?.id,
      userStage: userData?.stage,
      loanId: loanData?.id,
      assignedAdmin: adminId,
      callSId: reqData?.CallSid,
      callFrom: reqData?.CallFrom,
      callTo: reqData?.CallTo,
      callStartAt: new Date(reqData?.StartTime),
      callbackPreference,
      webhookReceivedAt: new Date(reqData?.CurrentTime),
      webhookResponse: reqData,
    };
    return await this.pg.create(exotelCallbackWaitlistEntity, payload);
  }

  async connectUserToAgent(reqData) {
    if (!reqData?.CallFrom) raiseParamMissing('Caller Phone');
    let userInput;

    const adminCompanyPhone = await this.getOrAssignAdmin(reqData?.CallFrom);
    if (reqData?.digits) userInput = JSON.parse(reqData?.digits);
    if (userInput == '3' || !adminCompanyPhone || !adminCompanyPhone.length)
      return await this.connectToSupport();
    return await this.validateExotelAdmin(adminCompanyPhone);
  }

  async getOrAssignAdmin(callerNum) {
    const userPhone = this.utils.removeLeadingZero(callerNum);
    const data = await this.query.getCompleteUserDetails(userPhone);
    const loanData = data?.loanData;
    const masterData = data?.userData?.masterData;

    let adminId;
    if (loanData?.loanStatus != kActiveLoan && masterData?.assignedCSE)
      adminId = masterData?.assignedCSE;
    else if (loanData?.loanStatus == kActiveLoan) {
      if (loanData?.followerId) {
        adminId = loanData?.followerId;
      } else adminId = masterData?.assignedCSE;
    }

    if (
      !adminId ||
      loanData?.loanStatus == kRejectLoan ||
      kSupportAdminIds.includes(adminId)
    )
      return null;

    const adminCompanyPhone =
      (await this.commonService.getAdminData(adminId))?.companyPhone || null;

    return adminCompanyPhone ? [`+91${adminCompanyPhone}`] : null;
  }

  async connectToSupport() {
    const supportNumbers = [
      Env.thirdParty.exotel.support_contact_1,
      Env.thirdParty.exotel.support_contact_2,
    ];
    return await this.validateExotelAdmin(supportNumbers, false);
  }

  async validateExotelAdmin(adminPhones: string[], allowFallback = true) {
    const dialNumbers = [];

    for (let i = 0; i < adminPhones?.length; i++) {
      const adminPhone = adminPhones[i];
      const queryParams = {
        fields: 'devices',
        'devices.contact_uri': adminPhone,
      };
      const exotelResponse = await this.apiService.get(
        exotelUserUrl,
        queryParams,
        exotelHeaders,
      );

      const exotelAdmin =
        exotelResponse?.response?.response[0]?.data?.devices[0];

      if (exotelAdmin?.available && exotelAdmin?.status == 'free')
        dialNumbers.push(adminPhone);
    }

    if (dialNumbers?.length) return dialNumbers.join(',');
    return allowFallback
      ? await this.connectToSupport() // Fallback to connect to support....
      : ''; // Fallback to schedule the callback....
  }
}
