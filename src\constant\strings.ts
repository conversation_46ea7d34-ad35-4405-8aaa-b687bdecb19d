export const kServerStr = {
  nestJsInit: 'Attempting to start the NestJs server...',
  runningPort: 'Server is running on port -> ',
};
export const kGlobalDateTrail = 'T10:00:00.000Z';
export const kUTCTrail = 'T18:30:00.000Z';
export const k1159Tail = '11:59 PM';

export const kMediaImages = 'mediaImages';

export const kRupee = '₹';
export const kPercent = '%';

export const kRedisClient = 'REDIS_CLIENT';

// To show the recent step of user in admin panel
export const kUserStageEnums = {
  PHONE_VERIFICATION: 'PHONE VERIFICATION',
  BASIC_DETAILS: 'BASIC DETAILS',
  SELFIE: 'SELFIE',
  PIN: 'PIN',
  AADHAAR: 'AADHAAR',
  EMPLOYMENT: 'EMPLOYMENT DETAILS',
  EXPRESS_REAPPLY: 'EXPRESS REAPPLY',
  BANKING: 'BANK VERIFICATION',
  FINAL_VERIFICATION: 'FINAL VERIFICATION',
  LOAN_ACCEPT: 'LOAN_ACCEPT',
  MANDATE: 'MANDATE',
  ESIGN: 'ESIGN',
  ON_HOLD: 'ON HOLD',
  NO_ROUTE: 'NO_ROUTE',
};

export const kEMIEnums = {
  ONTIME: 'ON_TIME',
  PREPAID: 'PRE_PAID',
  DEFAULTER: 'DEFAULTER',
  DELAYED: 'DELAYED',
  UPCOMING: 'UPCOMING',
};

export const kInProcessLoan = 'InProcess';
export const kActiveLoan = 'Active';
export const kAcceptedLoan = 'Accepted';
export const kCompleteLoan = 'Complete';
export const kRejectLoan = 'Rejected';

///  verification Progress
export const kPending = 'Pending';
export const kUnderVerification = 'Under Verification';
export const kUserBlocked = 'User Blocked!!';
export const kUserUnBlocked = 'User Unblocked!!';
export const kUserCoolOf = 'User in cool-off';
export const kSuccessful = 'Successful';

export const kCloudService = 'CLOUD_SERVICE';
export const kGoogle = 'GOOGLE';
export const kOracle = 'ORACLE';
export const kExotelRedisKey = 'EXOTEL_';
