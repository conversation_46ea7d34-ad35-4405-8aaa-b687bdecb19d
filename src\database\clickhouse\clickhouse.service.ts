// Imports
import {
  CLICKHOUSE_CACHING_DETAILS,
  CLICKHOUSE_USER_DETAILS,
} from './clickhouse.tables';
import { ClickHouseClient } from '@clickhouse/client';
import { Injectable, OnModuleInit } from '@nestjs/common';

@Injectable()
export class ClickhouseService implements OnModuleInit {
  constructor(private readonly client: ClickHouseClient) {}

  onModuleInit() {
    this.injectTables().catch((err) => {
      console.log(err);
    });
  }

  async injectQuery(queryStr: string, jsonResponse = true) {
    const result = await this.client.query({
      query: queryStr,
      format: jsonResponse ? 'JSON' : null,
    });

    if (!jsonResponse) {
      return result;
    }

    // Extract the actual data from the ResultSet
    const rows = await result.json();
    return rows.data ?? [];
  }

  async insert(table_name: string, list: any) {
    await this.client.insert({
      table: table_name,
      format: 'JSONEachRow',
      values: list,
    });
  }

  private async injectTables() {
    try {
      console.log('Started -> Injecting tables for clickhouse');

      await this.injectQuery(CLICKHOUSE_USER_DETAILS, null);
      await this.injectQuery(CLICKHOUSE_CACHING_DETAILS, null);

      console.log('Completed -> Injecting tables for clickhouse');
    } catch (error) {
      console.log(error);
    }
  }
}
