// Imports
import { Injectable } from '@nestjs/common';
import { getMD5Hash } from 'src/utils/crypt';
import { PgService } from 'src/database/pg/pg.service';
import { raiseBadRequest, raiseParamMissing } from 'src/config/error';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { CallKaroEntity } from 'src/database/pg/entities/callKaro.entity';

@Injectable()
export class CallKaroQuery {
  constructor(private readonly pg: PgService) {}

  async dataForCallerData(reqData) {
    let number: string = reqData?.number;
    if (!number) {
      raiseParamMissing('number');
    }
    number = number.replace(/\)/g, '');
    number = number.replace(/\(/g, '');
    number = number.replace(/ /g, '');
    number = number.replace('+91', '');
    number = number.slice(-10);

    if (number.length != 10) {
      raiseBadRequest('number');
    }

    // return {};
    // Purpose -> Testing (Team CallKaro)
    if (number == '7602045111') {
      return {
        name: 'Abhilash Datta',
        gender: 'MALE',
        loan_status: 'InProcess',
        loan_tenure_in_days: null,
        loan_amount: null,
        interest_rate_per_anuum: Math.floor(+'0.1' * 365),
      };
    } else if (number == '6353887681') {
      return {
        name: 'Rutul Patel',
        gender: 'MALE',
        loan_status: 'InProcess',
        loan_tenure_in_days: 98,
        loan_amount: 35000,
        interest_rate_per_anuum: 36.5,
        emi_count: 4,
        emi_amount: 10000,
        first_emi_date: '2025-08-01',
      };
    } else if (number == '6353887681') {
      return {
        name: 'Rahil Patel',
        gender: 'MALE',
        loan_status: 'InProcess',
        loan_tenure_in_days: 98,
        loan_amount: 35000,
        interest_rate_per_anuum: 36.5,
        emi_count: 4,
        emi_amount: 10000,
        first_emi_date: '2025-08-01',
      };
    } else if (number == '6202230797') {
      return {
        name: 'Rutul Patel',
        gender: 'MALE',
        loan_status: 'InProcess',
        loan_tenure_in_days: 98,
        loan_amount: 35000,
        interest_rate_per_anuum: 36.5,
        emi_count: 4,
        emi_amount: 10000,
        first_emi_date: '2025-08-01',
      };
    }

    const hashPhone = getMD5Hash(number);
    const user_data: registeredUsers = await this.pg.findOne(registeredUsers, {
      attributes: ['fullName', 'gender', 'lastLoanId'],
      where: { hashPhone },
    });

    let loan_data: loanTransaction;
    if (user_data.lastLoanId) {
      loan_data = await this.pg.findOne(loanTransaction, {
        attributes: [
          'loanStatus',
          'approvedDuration',
          'interestRate',
          'netApprovedAmount',
          'netEmiData',
        ],
        where: { id: user_data.lastLoanId },
      });
    }

    const emi_list = [];
    let emi_amount: number | string = 0;
    let first_emi_date = '';

    if (loan_data?.netEmiData) {
      loan_data?.netEmiData.forEach((el, index) => {
        const emi = JSON.parse(el);
        emi_list.push(emi);
        emi_amount = emi.Emi ?? '0';
        if (index == 0) {
          first_emi_date = emi.Date.substring(0, 10);
        }
      });
    }

    return {
      name: user_data.fullName ?? 'User',
      gender: user_data.gender,
      loan_status: loan_data.loanStatus,
      loan_tenure_in_days: loan_data.approvedDuration,
      loan_amount: loan_data.netApprovedAmount,
      interest_rate_per_anuum: +(loan_data.interestRate ?? '0.1') * 365,
      emi_count: emi_list.length ?? '0',
      emi_amount: emi_amount ?? '0',
      first_emi_date,
    };
  }

  async updateCallKaro(callSid, body) {
    return await this.pg.updateWithWhere(
      CallKaroEntity,
      { response: body },
      {
        where: { call_sid: callSid },
      },
    );
  }
}
